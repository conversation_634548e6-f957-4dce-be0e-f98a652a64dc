#!/usr/bin/env tsx

/**
 * 测试控制台错误修复效果
 * 验证 favicon 和认证接口的改进
 */

import { launch } from 'puppeteer';

async function testConsoleErrors() {
  console.log('🔍 测试控制台错误修复效果...\n');

  const browser = await launch({ 
    headless: false, // 设为 false 以便观察
    devtools: true 
  });
  
  try {
    const page = await browser.newPage();
    
    // 监听控制台消息
    const consoleMessages: string[] = [];
    const networkErrors: string[] = [];
    
    page.on('console', (msg) => {
      const text = msg.text();
      consoleMessages.push(text);
      console.log(`📝 Console: ${text}`);
    });
    
    page.on('response', (response) => {
      if (!response.ok()) {
        const error = `${response.status()} ${response.url()}`;
        networkErrors.push(error);
        console.log(`❌ Network Error: ${error}`);
      }
    });

    console.log('🌐 访问首页...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    // 等待页面完全加载
    await page.waitForTimeout(3000);

    console.log('\n📊 测试结果分析:');
    
    // 检查 favicon 错误
    const faviconErrors = networkErrors.filter(error => error.includes('favicon.ico'));
    if (faviconErrors.length === 0) {
      console.log('✅ Favicon: 无 404 错误');
    } else {
      console.log('❌ Favicon: 仍有错误', faviconErrors);
    }
    
    // 检查认证接口错误
    const authErrors = networkErrors.filter(error => error.includes('/api/auth/me'));
    if (authErrors.length === 0) {
      console.log('✅ 认证接口: 无网络错误');
    } else {
      console.log('ℹ️ 认证接口: 401 错误是正常的（用户未登录）', authErrors);
    }
    
    // 检查 React DevTools 提示
    const devToolsMessages = consoleMessages.filter(msg => 
      msg.includes('React DevTools') || msg.includes('react-devtools')
    );
    if (devToolsMessages.length > 0) {
      console.log('ℹ️ React DevTools: 开发提示正常显示（仅开发环境）');
    }
    
    // 检查其他错误
    const otherErrors = consoleMessages.filter(msg => 
      msg.includes('Error') || msg.includes('error') || msg.includes('Failed')
    ).filter(msg => 
      !msg.includes('React DevTools') && 
      !msg.includes('Failed to fetch user session')
    );
    
    if (otherErrors.length === 0) {
      console.log('✅ 其他错误: 无异常错误');
    } else {
      console.log('⚠️ 其他错误:', otherErrors);
    }

    console.log('\n🎯 测试登录流程...');
    
    // 测试登录页面
    await page.goto('http://localhost:3000/login');
    await page.waitForTimeout(2000);
    
    // 测试注册页面
    await page.goto('http://localhost:3000/register');
    await page.waitForTimeout(2000);
    
    console.log('\n✅ 测试完成！');
    console.log('\n📋 总结:');
    console.log(`- 控制台消息总数: ${consoleMessages.length}`);
    console.log(`- 网络错误总数: ${networkErrors.length}`);
    console.log(`- Favicon 错误: ${faviconErrors.length}`);
    console.log(`- 认证相关: ${authErrors.length} (正常)`);
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  } finally {
    await browser.close();
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/health');
    return response.ok;
  } catch {
    return false;
  }
}

async function main() {
  console.log('🚀 开始测试控制台错误修复...\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ 服务器未运行，请先启动开发服务器:');
    console.log('   npm run dev');
    process.exit(1);
  }
  
  await testConsoleErrors();
}

if (require.main === module) {
  main().catch(console.error);
}
