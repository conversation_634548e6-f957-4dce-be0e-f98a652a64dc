"use client";

import { useEffect, useState } from 'react';

export default function DebugConfigPage() {
  const [status, setStatus] = useState('开始测试...');
  const [configs, setConfigs] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testAPI = async () => {
      try {
        setStatus('正在调用 API...');
        console.log('🔍 开始测试 API 调用');
        
        const response = await fetch('/api/config/databases');
        console.log('📡 API 响应状态:', response.status, response.statusText);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('📋 API 响应数据:', result);
        
        if (result.success) {
          setConfigs(result.data);
          setStatus('API 调用成功');
        } else {
          throw new Error(result.error || 'API 返回失败');
        }
        
      } catch (err) {
        console.error('❌ API 调用失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
        setStatus('API 调用失败');
      }
    };

    testAPI();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">配置调试页面</h1>
      
      <div className="mb-4">
        <h2 className="text-lg font-semibold">状态:</h2>
        <p className={error ? 'text-red-600' : 'text-green-600'}>{status}</p>
      </div>
      
      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 rounded">
          <h2 className="text-lg font-semibold text-red-800">错误:</h2>
          <p className="text-red-600">{error}</p>
        </div>
      )}
      
      {configs && (
        <div className="mb-4 p-4 bg-green-100 border border-green-400 rounded">
          <h2 className="text-lg font-semibold text-green-800">配置数据:</h2>
          <pre className="text-sm text-green-600 overflow-auto">
            {JSON.stringify(configs, null, 2)}
          </pre>
        </div>
      )}
      
      <div className="mt-8">
        <button 
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          onClick={() => window.location.reload()}
        >
          重新测试
        </button>
      </div>
    </div>
  );
}
