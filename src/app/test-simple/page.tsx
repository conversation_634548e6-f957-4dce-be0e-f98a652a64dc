"use client";

import { useEffect, useState } from "react";

export default function TestSimplePage() {
  const [loading, setLoading] = useState(true);
  const [configs, setConfigs] = useState<any>({});
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('开始获取配置...');
        const response = await fetch('/api/config/databases');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        
        const result = await response.json();
        console.log('获取到配置:', result);
        
        if (result.success) {
          setConfigs(result.data);
        } else {
          throw new Error('API失败');
        }
      } catch (err) {
        console.error('错误:', err);
        setError(err instanceof Error ? err.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1>测试页面</h1>
        <p>加载中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1>测试页面</h1>
        <p className="text-red-600">错误: {error}</p>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1>测试页面</h1>
      <p className="text-green-600">成功加载配置!</p>
      <div className="mt-4">
        <h2>数据库列表:</h2>
        <ul>
          {Object.entries(configs).map(([code, config]: [string, any]) => (
            <li key={code} className="mb-2">
              <strong>{code}</strong>: {config.name} ({config.category})
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
